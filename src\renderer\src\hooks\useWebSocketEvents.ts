/**
 * WebSocket Events Hook
 * Custom hook for handling WebSocket events from the main process
 */

import { useEffect, useCallback } from 'react'

/**
 * WebSocket event handler function type
 */
type EventHandler = (data: unknown) => void

/**
 * WebSocket event listener configuration
 */
interface WebSocketEventConfig {
  /** Event name to listen for */
  eventName: string
  /** Handler function to call when event is received */
  handler: EventHandler
  /** Whether the listener is enabled (default: true) */
  enabled?: boolean
}

/**
 * Hook for listening to WebSocket events from the main process
 * @param config - Event listener configuration
 * @returns Cleanup function to remove the listener
 */
export const useWebSocketEvent = (config: WebSocketEventConfig): (() => void) => {
  const { eventName, handler, enabled = true } = config

  const handleBrokerEvent = useCallback(
    (event: string, data: unknown) => {
      if (event === eventName) {
        handler(data)
      }
    },
    [eventName, handler]
  )

  useEffect(() => {
    if (!enabled) return

    // Set up the event listener for broker events
    const cleanup = window.api.on('broker:event', handleBrokerEvent)

    return cleanup
  }, [handleBrokerEvent, enabled])

  // Return cleanup function for manual cleanup if needed
  return useCallback(() => {
    // The cleanup is handled by the useEffect return function
    // This is provided for consistency with the hook pattern
  }, [])
}

/**
 * Hook specifically for listening to balance updates
 * @param onBalanceUpdate - Function to call when balance is updated
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 */
export const useBalanceWebSocketEvent = (
  onBalanceUpdate: (balance: number) => void,
  enabled: boolean = true
): (() => void) => {
  const handleBalanceUpdate = useCallback(
    (data: unknown) => {
      // The balance data should be a number based on the broker implementation
      if (typeof data === 'number' && Number.isFinite(data)) {
        onBalanceUpdate(data)
      } else {
        console.warn('Received invalid balance data from WebSocket:', data)
      }
    },
    [onBalanceUpdate]
  )

  return useWebSocketEvent({
    eventName: 'balance', // This matches BROADCAST_EVENTS.ACCOUNT_BALANCE
    handler: handleBalanceUpdate,
    enabled
  })
}

/**
 * Hook for listening to multiple WebSocket events
 * @param configs - Array of event listener configurations
 * @returns Array of cleanup functions for each listener
 */
export const useMultipleWebSocketEvents = (configs: WebSocketEventConfig[]): (() => void)[] => {
  return configs.map((config) => useWebSocketEvent(config))
}

/**
 * Hook for listening to connection state changes
 * @param onConnectionChange - Function to call when connection state changes
 * @param enabled - Whether the listener is enabled (default: true)
 * @returns Cleanup function to remove the listener
 */
export const useConnectionWebSocketEvent = (
  onConnectionChange: (state: string) => void,
  enabled: boolean = true
): (() => void) => {
  const handleConnectionChange = useCallback(
    (data: unknown) => {
      if (typeof data === 'string') {
        onConnectionChange(data)
      }
    },
    [onConnectionChange]
  )

  return useWebSocketEvent({
    eventName: 'state_change',
    handler: handleConnectionChange,
    enabled
  })
}
